# === Optimized 3D Lane Detection Based on Standard BEVFusion with Embedding ===
dataset_type: Custom3DLaneDataset
dataset_root: /turbo/pcpt/data/lane_dataset/lane_3D_labeldata_0606_500frames
cam_list: ['60_front', '120_front', '120_left', '120_right', '120_back', 'right_back', 'left_back']
reduce_beams: 32
load_dim: 4
use_dim: 4  # Use all 4 dimensions: [x, y, z, intensity]
load_augmented: null

# Image size for all cameras (adapted from MogoB2 settings)
image_size: [256, 704]
max_epochs: 10

fp16:
  loss_scale:
    growth_interval: 2000

# === CRITICAL: Unified coordinate system for consistent feature dimensions ===
# Focus on forward 0-60m range with high resolution for lane detection
# Using 0.1m resolution for fine-grained lane detection
voxel_size: [0.1, 0.1, 0.2]  # Higher resolution: 10cm x 10cm x 20cm
point_cloud_range: [0.0, -12.0, -1.0, 60.0, 12.0, 3.0]  # Forward-focused: [x_min, y_min, z_min, x_max, y_max, z_max]

camera_num: 7

# === Camera Augmentation Settings (per camera, adapted from MogoB2) ===
augment2d:
  # Resize limits per camera: [60_front, 120_front, 120_left, 120_right, 120_back, right_back, left_back]
  resize_train: [[0.184, 0.187], [0.38, 0.42], [0.38, 0.42], [0.38, 0.42], [0.38, 0.55], [0.38, 0.42], [0.38, 0.42]]
  resize_test: [[0.185, 0.185], [0.4, 0.4], [0.4, 0.4], [0.4, 0.4], [0.47, 0.47], [0.4, 0.4], [0.4, 0.4]]
  # Bottom crop limits per camera
  bot_pct_lim_train: [[0.0, 0.0], [0.12, 0.15], [0.2, 0.25], [0.2, 0.25], [0.15, 0.20], [0.2, 0.25], [0.2, 0.25]]
  bot_pct_lim_test: [[0.0, 0.0], [0.14, 0.14], [0.22, 0.22], [0.22, 0.22], [0.17, 0.17], [0.22, 0.22], [0.22, 0.22]]

# === Lane Task Specific ===
# Lane classes with IDs matching lane_line_config_v1.yaml (1-indexed)
lane_classes:
  - unknown # ID 0 (dummy class for background)
  - white-solid # ID 1
  - white-dashed # ID 2
  - white-double-solid # ID 3
  - white-solid-dashed # ID 4
  - white-dashed-solid # ID 5
  - white-double-dashed # ID 6
  - yellow-solid # ID 7
  - yellow-dashed # ID 8
  - yellow-double-solid # ID 9 
  - yellow-solid-dashed # ID 10
  - left-yellow-right-white-double-solid # ID 11
  - road-edge-dashed # ID 12

# === Input Modality ===
input_modality:
  use_lidar: true
  use_camera: true
  use_radar: false
  use_map: false
  use_external: false

# === Data Pipelines ===
train_pipeline:
  - type: LoadMultiViewImageFromFiles
    to_float32: true
  - type: LoadPointsFromFile
    coord_type: LIDAR
    load_dim: ${load_dim}
    use_dim: ${use_dim}
    reduce_beams: ${reduce_beams}
    with_lidarid: False
  - type: LoadLaneAnnotations3D
    with_lane_3d: true
    with_lane_label_3d: true

  # Optional: Load dense depth map for front camera (120_front)
  # Uncomment if depth maps are available
  # - type: LoadDenseDepthMapFromFile
  #   to_float32: true
  #   depth_scale: 256.0
  #   default_depth: 30.0
  #   max_depth: 80.0
  #   min_depth: 0.1
  
  # Image augmentation adapted from MogoB2Dataset
  - type: ImageAug3D
    final_dim: ${image_size}
    camera_num: ${camera_num}
    resize_lim: ${augment2d.resize_train}  # Per-camera resize limits
    bot_pct_lim: ${augment2d.bot_pct_lim_train}  # Per-camera bottom crop limits
    rot_lim: [-5.4, 5.4]  # Rotation in degrees (adapted from MogoB2)
    rand_flip: true  # Enable random flip
    is_train: true
  
  - type: GlobalRotScaleTrans
    resize_lim: [1.0, 1.0]  # No resize for debugging
    rot_lim: [0.0, 0.0]  # No rotation for debugging
    trans_lim: 0.0  # No translation for debugging
    is_train: true
  
  - type: PointsRangeFilter
    point_cloud_range: ${point_cloud_range}
  
  - type: ImageNormalize
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
  
  - type: GenerateBEVLaneHeatmapTargets
    point_cloud_range: ${point_cloud_range}
    voxel_size: ${voxel_size}
    # CRITICAL: Grid size calculated from point_cloud_range and voxel_size
    # X: 60m / 0.1m = 600, Y: 24m / 0.1m = 240 (both multiples of 8)
    grid_size: [600, 240]  # Matches vtransform resolution exactly
    lane_classes: ${lane_classes}
      
    target_config:
      gaussian_sigma: 1.2  # Larger sigma for higher resolution
      heatmap_radius: 4    # Larger radius for higher resolution
      max_lanes: 40
      num_points: 120      # More points for longer 60m range
      generate_instance_ids: true  # CRITICAL: Enable for embedding training
  
  - type: DefaultFormatBundle3D
    classes: ${lane_classes}
  
  - type: Collect3D
    keys:
      - img
      - points
      - gt_lanes_3d
      - gt_lane_labels
      - lane_targets
    meta_keys:
      - camera_intrinsics
      - camera2ego
      - lidar2ego
      - lidar2camera
      - camera2lidar
      - lidar2image
      - img_aug_matrix
      - lidar_aug_matrix

test_pipeline:
  - type: LoadMultiViewImageFromFiles
    to_float32: true

  - type: LoadPointsFromFile
    coord_type: LIDAR
    load_dim: ${load_dim}
    use_dim: ${use_dim}
    reduce_beams: ${reduce_beams}

  - type: LoadLaneAnnotations3D
    with_lane_3d: true
    with_lane_label_3d: true
  
  # Image augmentation for test (no augmentation)
  - type: ImageAug3D
    final_dim: ${image_size}
    camera_num: ${camera_num}
    resize_lim: ${augment2d.resize_test}  # Per-camera resize limits
    bot_pct_lim: ${augment2d.bot_pct_lim_test}  # Per-camera bottom crop limits
    rot_lim: [0.0, 0.0]  # No rotation for test
    rand_flip: false  # No flip for test
    is_train: false
  
  - type: ImageNormalize
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
  - type: DefaultFormatBundle3D
    classes: ${lane_classes}
  - type: Collect3D
    keys:
      - img
      - points
      - gt_lanes_3d
      - gt_lane_labels
    meta_keys:
      - camera_intrinsics
      - camera2ego
      - lidar2ego
      - lidar2camera
      - camera2lidar
      - lidar2image
      - img_aug_matrix
      - lidar_aug_matrix

# --- Data Loading Config ---
data:
  samples_per_gpu: 1  # Single GPU debugging
  workers_per_gpu: 0  # No multiprocessing for debugging
  train:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "/train_annotations_skip20_without_depth_maps.pkl"}
    pipeline: ${train_pipeline}
    modality: ${input_modality}
    test_mode: false
    cam_list: ${cam_list}
    lane_classes: ${lane_classes}
    point_cloud_range: ${point_cloud_range}
  val:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "/val_annotations.pkl"}
    pipeline: ${test_pipeline}
    modality: ${input_modality}
    test_mode: false
    box_type_3d: LiDAR
    cam_list: ${cam_list}
    lane_classes: ${lane_classes}
    point_cloud_range: ${point_cloud_range}
  test:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "/test_info.pkl"}
    pipeline: ${test_pipeline}
    modality: ${input_modality}
    test_mode: true
    box_type_3d: LiDAR
    cam_list: ${cam_list}
    lane_classes: ${lane_classes}
    point_cloud_range: ${point_cloud_range}

# --- Evaluation ---
evaluation:
  interval: 1
  pipeline: ${test_pipeline}
  metric: 'OpenLane'
  eval_params:
     metric_list: ['f1_score', 'precision', 'recall', 'x_error_near', 'x_error_far', 'z_error']
     iou_threshold: 0.5

# --- Model Definition ---
model:
  type: BEVFusionForLanes
  encoders:
    camera:
      backbone:
        type: GPUNetWrapper
        model_type: GPUNet-1
        precision: fp16
        img_height: ${image_size[0]}
        img_width: ${image_size[1]}
        out_indices: [5, 10, 14]
        latency: 0.85ms
        gpuType: orin
        batch: 1
        pretrained: /configs/batch1/GV100/0.85ms.pth.tar
      neck:
        in_channels: [96, 288, 448]
      vtransform:
        # CRITICAL: Use same coordinate range and resolution as LiDAR for feature alignment
        # X: 60m / 0.4m = 150 grid cells, Y: 24m / 0.4m = 60 grid cells
        xbound: [0.0, 60.0, 0.4]    
        ybound: [-12.0, 12.0, 0.4]   
        zbound: [-1.0, 3.0, 4.0]     # Vertical range for BEV pooling
        dbound: [1.0, 60.0, 0.5]     # Depth from 1m to 60m with 0.5m resolution
    lidar:
      voxelize:
        point_cloud_range: ${point_cloud_range}
        voxel_size: ${voxel_size}
        max_voxels: [120000, 160000] 
      backbone:
        in_channels: ${use_dim}  # Match point cloud dimensions: [x, y, z, intensity]
        sparse_shape: [600, 240, 21]  # Exactly matches vtransform output dimensions
        encoder_paddings:
          - [0, 0, 1]
          - [0, 0, [1, 1, 0]]
          - [0, 0, 1]
          - [0, 0]
        encoder_strides:
          - [0, 0, 2]        # Layer 1: No XY stride, Z stride 2: [240, 120, 10]
          - [0, 0, 2]        # Layer 2: No XY stride, Z stride 2: [240, 120, 5] 
          - [0, 0, [2, 2, 1]] # Layer 3: XY stride 2, Z stride 1: [120, 60, 5]
          - [0, 0]           # Layer 4: No stride: [120, 60, 5]

  # CRITICAL: Fuser configuration with matching channel dimensions
  fuser:
    type: ConvFuser
    in_channels: [80, 128]  # Camera features (80) + LiDAR features (128)
    out_channels: 256

  decoder:
    backbone:
      type: SECOND
      in_channels: 256
      out_channels: [128, 64, 128, 256]  # Four levels for better feature hierarchy
      layer_nums: [1, 2, 3, 1]
      layer_strides: [1, 1, 2, 2]  # Final features will be 1/4 resolution: [60, 30]
    neck:
      type: SECONDFPN
      in_channels: [128, 64, 128, 256]
      out_channels: [128, 128, 128, 128]
      out_channel: 128
      upsample_strides: [1, 1, 2, 2]     # 对应backbone的strides
      use_conv_for_no_stride: true

  heads:
    lane:
      type: BEVLaneHeatmapHead
      in_channels: 512  # From concatenated FPN output (256 + 256)
      feat_channels: 128
      num_classes: ${len(lane_classes)}
      grid_conf:
        # CRITICAL: Must match vtransform and target generation exactly
        xbound: [0.0, 60.0, 0.25]    # Forward 0-60m with 25cm resolution
        ybound: [-15.0, 15.0, 0.25]  # Side ±15m with 25cm resolution
      row_points: 150   # More points for 60m range
      z_range: [-2.0, 2.0]
      max_lanes: 40
      hm_thres: 0.25
      nms_kernel_size: 5
      use_sigmoid: true
      
      # === EMBEDDING CONFIGURATION ===
      use_embedding: true
      embedding_dim: 16  # Increased from default 8 for better discriminative power
      group_lanes: true  # CRITICAL: Enable to use embedding-based grouping
      clustering_method: 'dbscan'  # Use DBSCAN clustering for embedding-based grouping
      
      # Optimized DBSCAN parameters for BEV grid scale (25cm resolution)
      clustering_epsilon: 1.5  # Adjusted for BEV grid resolution (0.25m per cell)
      clustering_min_points: 5  # Minimum points for robust lane instance
      lane_group_min_distance: 2.0  # Geometric fallback distance
      
      # === LOSS CONFIGURATION ===
      loss_heatmap:
        type: GaussianFocalLoss
        alpha: 2.0
        gamma: 4.0
        reduction: mean
        loss_weight: 2.0
      loss_offset:
        type: L1Loss
        reduction: mean
        loss_weight: 1.5
      loss_z:
        type: L1Loss
        reduction: mean
        loss_weight: 1.5
      loss_cls:
        type: CrossEntropyLoss
        use_sigmoid: false
        reduction: mean
        loss_weight: 1.0
      
      # === EMBEDDING LOSS CONFIGURATION ===
      loss_embedding:
        type: DiscriminativeLoss
        delta_v: 0.8    # Pull margin - adjusted for BEV scale
        delta_d: 2.5    # Push margin - increased for better separation
        norm: 2         # L2 norm for distance calculation
        alpha: 1.5      # Pull loss weight - increased importance
        beta: 1.0       # Push loss weight
        gamma: 0.0005   # Regularization loss weight - reduced
        reduction: mean
        loss_weight: 1.2  # Overall embedding loss weight

# --- Training Settings ---
optimizer:
  type: AdamW
  lr: 0.0001  # Lower learning rate for debugging
  weight_decay: 0.01
  betas: [0.9, 0.999]

lr_config:
  policy: CosineAnnealing
  warmup: linear
  warmup_iters: 100  # Shorter warmup for debugging
  warmup_ratio: 0.33333333
  min_lr_ratio: 1.0e-6